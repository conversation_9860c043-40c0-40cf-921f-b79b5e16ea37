#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试同花顺板块数据Excel导出工具
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ths_plate_data_excel import THSPlateDataFetcher, ExcelExporter


def test_concept_plates_fetching():
    """测试概念板块获取功能"""
    print("=" * 60)
    print("测试概念板块获取功能")
    print("=" * 60)
    
    fetcher = THSPlateDataFetcher()
    
    # 测试获取所有概念板块
    print("\n1. 测试获取所有概念板块名称...")
    all_concepts = fetcher.fetch_all_concept_plates()
    
    if all_concepts:
        print(f"✅ 成功获取 {len(all_concepts)} 个概念板块")
        print("\n前10个概念板块:")
        for i, concept in enumerate(all_concepts[:10]):
            print(f"  {i+1:2d}. {concept['name']} (代码: {concept['code']})")
        
        # 显示一些特殊的概念板块
        special_concepts = ['人工智能', '新能源汽车', '5G', '芯片概念', '医疗器械']
        print(f"\n查找特殊概念板块:")
        for special in special_concepts:
            found = [c for c in all_concepts if special in c['name']]
            if found:
                print(f"  ✅ 找到 '{special}': {found[0]['name']} (代码: {found[0]['code']})")
            else:
                print(f"  ❌ 未找到 '{special}'")
    else:
        print("❌ 获取概念板块失败")
    
    return all_concepts


def test_hot_data_fetching():
    """测试热门数据获取功能"""
    print("\n" + "=" * 60)
    print("测试热门数据获取功能")
    print("=" * 60)
    
    fetcher = THSPlateDataFetcher()
    
    # 测试获取热门概念板块数据
    print("\n1. 测试获取热门概念板块数据...")
    hot_data = fetcher.fetch_plate_data('concept')
    
    if hot_data:
        plate_list = hot_data.get('data', {}).get('plate_list', [])
        print(f"✅ 成功获取 {len(plate_list)} 条热门概念板块数据")
        
        print("\n热门概念板块:")
        for i, plate in enumerate(plate_list[:5]):
            name = plate.get('name', '')
            rise_fall = plate.get('rise_and_fall', 0)
            rate = plate.get('rate', 0)
            print(f"  {i+1}. {name}: {rise_fall}% (成交额: {rate}万)")
    else:
        print("❌ 获取热门概念板块数据失败")
    
    return hot_data


def test_data_integration():
    """测试数据整合功能"""
    print("\n" + "=" * 60)
    print("测试数据整合功能")
    print("=" * 60)
    
    fetcher = THSPlateDataFetcher()
    
    # 测试获取整合后的概念板块数据
    print("\n1. 测试获取整合后的概念板块数据...")
    concept_data = fetcher.get_concept_data()
    
    if concept_data is not None and not concept_data.empty:
        print(f"✅ 成功整合概念板块数据，共 {len(concept_data)} 条")
        
        # 统计数据来源
        if '数据来源' in concept_data.columns:
            source_counts = concept_data['数据来源'].value_counts()
            print(f"\n数据来源统计:")
            for source, count in source_counts.items():
                print(f"  {source}: {count} 条")
        
        # 显示有实时数据的板块
        if '涨跌幅(%)' in concept_data.columns:
            active_plates = concept_data[concept_data['涨跌幅(%)'] != 0]
            print(f"\n有实时数据的板块: {len(active_plates)} 个")
            
            if not active_plates.empty:
                print("涨幅前5名:")
                top_gainers = active_plates.nlargest(5, '涨跌幅(%)')
                for _, row in top_gainers.iterrows():
                    print(f"  {row['板块名称']}: {row['涨跌幅(%)']}%")
    else:
        print("❌ 获取整合概念板块数据失败")
    
    return concept_data


def test_excel_export():
    """测试Excel导出功能"""
    print("\n" + "=" * 60)
    print("测试Excel导出功能")
    print("=" * 60)
    
    fetcher = THSPlateDataFetcher()
    
    # 获取数据
    print("\n1. 获取数据...")
    concept_data, industry_data = fetcher.get_all_data()
    
    if concept_data is not None or industry_data is not None:
        print("✅ 数据获取成功")
        
        # 导出Excel
        print("\n2. 导出Excel...")
        try:
            filename = "测试_同花顺板块数据.xlsx"
            excel_file = ExcelExporter.export_to_excel(concept_data, industry_data, filename)
            print(f"✅ Excel导出成功: {excel_file}")
            
            # 检查文件大小
            if os.path.exists(excel_file):
                file_size = os.path.getsize(excel_file) / 1024  # KB
                print(f"📏 文件大小: {file_size:.1f} KB")
            
        except Exception as e:
            print(f"❌ Excel导出失败: {e}")
    else:
        print("❌ 数据获取失败，无法测试Excel导出")


def main():
    """主测试函数"""
    print("🧪 开始测试同花顺板块数据Excel导出工具")
    print("=" * 80)
    
    try:
        # 测试各个功能模块
        test_concept_plates_fetching()
        test_hot_data_fetching()
        test_data_integration()
        test_excel_export()
        
        print("\n" + "=" * 80)
        print("🎉 所有测试完成!")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
