#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同花顺板块数据获取工具 - Excel专用版
获取概念板块和行业板块数据，直接输出到Excel文件
"""

import json
import logging
import os
import sys
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple

import pandas as pd
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# API配置
API_CONFIG = {
    'concept_url': 'https://dq.10jqka.com.cn/fuyao/hot_list_data/out/hot_list/v1/plate?type=concept',
    'industry_url': 'https://dq.10jqka.com.cn/fuyao/hot_list_data/out/hot_list/v1/plate?type=industry',
    'timeout': 30,
    'max_retries': 3,
    'backoff_factor': 1
}

# 请求头配置
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Referer': 'https://dq.10jqka.com.cn/',
}


class THSPlateDataFetcher:
    """同花顺板块数据获取器"""
    
    def __init__(self):
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """创建带重试机制的请求会话"""
        session = requests.Session()

        # 禁用代理
        session.proxies = {
            'http': None,
            'https': None,
        }

        # 配置重试策略
        retry_strategy = Retry(
            total=API_CONFIG['max_retries'],
            backoff_factor=API_CONFIG['backoff_factor'],
            status_forcelist=[429, 500, 502, 503, 504],
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        session.headers.update(HEADERS)

        return session
    
    def fetch_plate_data(self, plate_type: str) -> Optional[Dict]:
        """
        获取板块数据
        
        Args:
            plate_type: 板块类型，'concept' 或 'industry'
            
        Returns:
            Dict: API响应数据，失败时返回None
        """
        if plate_type not in ['concept', 'industry']:
            raise ValueError("plate_type must be 'concept' or 'industry'")
        
        url = API_CONFIG['concept_url'] if plate_type == 'concept' else API_CONFIG['industry_url']
        
        try:
            logger.info(f"正在获取{plate_type}板块数据...")
            response = self.session.get(url, timeout=API_CONFIG['timeout'])
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('status_code') != 0:
                logger.error(f"API返回错误: {data.get('status_msg', 'Unknown error')}")
                return None
            
            logger.info(f"成功获取{plate_type}板块数据，共{len(data['data']['plate_list'])}条记录")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"获取{plate_type}板块数据失败: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"解析{plate_type}板块数据JSON失败: {e}")
            return None
        except Exception as e:
            logger.error(f"获取{plate_type}板块数据时发生未知错误: {e}")
            return None
    
    def get_concept_data(self) -> Optional[pd.DataFrame]:
        """获取概念板块数据"""
        data = self.fetch_plate_data('concept')
        if data:
            df = pd.DataFrame(data['data']['plate_list'])
            df['plate_type'] = '概念板块'
            now = datetime.now()
            df['获取时间'] = now.strftime('%Y-%m-%d %H:%M:%S')
            # 数据类型转换和清洗
            df = self._clean_and_format_data(df)
            return df
        return None

    def get_industry_data(self) -> Optional[pd.DataFrame]:
        """获取行业板块数据"""
        data = self.fetch_plate_data('industry')
        if data:
            df = pd.DataFrame(data['data']['plate_list'])
            df['plate_type'] = '行业板块'
            now = datetime.now()
            df['获取时间'] = now.strftime('%Y-%m-%d %H:%M:%S')
            # 数据类型转换和清洗
            df = self._clean_and_format_data(df)
            return df
        return None

    def _clean_and_format_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗和格式化数据"""
        if df.empty:
            return df

        # 重命名列为中文
        column_mapping = {
            'name': '板块名称',
            'code': '板块代码',
            'rise_and_fall': '涨跌幅(%)',
            'etf_rise_and_fall': 'ETF涨跌幅(%)',
            'rate': '成交额(万元)',
            'hot_rank_chg': '热度排名变化',
            'hot_tag': '热度标签',
            'tag': '标签信息',
            'etf_name': 'ETF名称',
            'etf_product_id': 'ETF产品ID',
            'market_id': '市场ID',
            'etf_market_id': 'ETF市场ID',
            'order': '排序',
            'plate_type': '板块类型'
        }
        
        # 重命名存在的列
        for old_name, new_name in column_mapping.items():
            if old_name in df.columns:
                df = df.rename(columns={old_name: new_name})

        # 数值型列处理
        numeric_columns = ['涨跌幅(%)', 'ETF涨跌幅(%)', '成交额(万元)', '热度排名变化']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # 字符串列清理
        string_columns = ['板块名称', '板块代码', '热度标签', '标签信息', 'ETF名称']
        for col in string_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip()
                df[col] = df[col].replace(['', 'nan', 'None'], None)

        return df
    
    def get_all_data(self) -> Tuple[Optional[pd.DataFrame], Optional[pd.DataFrame]]:
        """获取所有板块数据"""
        logger.info("开始获取所有板块数据...")
        concept_data = self.get_concept_data()
        time.sleep(1)  # 避免请求过于频繁
        industry_data = self.get_industry_data()
        return concept_data, industry_data


class ExcelExporter:
    """Excel导出器"""
    
    @staticmethod
    def export_to_excel(concept_df: Optional[pd.DataFrame], 
                       industry_df: Optional[pd.DataFrame], 
                       filename: Optional[str] = None) -> str:
        """
        导出数据到Excel文件
        
        Args:
            concept_df: 概念板块数据
            industry_df: 行业板块数据
            filename: 文件名，如果为None则自动生成
            
        Returns:
            str: 导出的文件路径
        """
        # 确保输出到result目录
        result_dir = os.path.join(os.path.dirname(__file__), 'result')
        os.makedirs(result_dir, exist_ok=True)

        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(result_dir, f"同花顺板块数据_{timestamp}.xlsx")
        elif not os.path.isabs(filename):
            filename = os.path.join(result_dir, filename)
        
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 导出概念板块数据
                if concept_df is not None and not concept_df.empty:
                    display_df = ExcelExporter._prepare_display_data(concept_df)
                    display_df.to_excel(writer, sheet_name='概念板块', index=False)
                    
                    # 格式化工作表
                    worksheet = writer.sheets['概念板块']
                    ExcelExporter._format_worksheet(worksheet, len(display_df))
                    logger.info(f"概念板块数据已导出，共{len(concept_df)}条记录")

                # 导出行业板块数据
                if industry_df is not None and not industry_df.empty:
                    display_df = ExcelExporter._prepare_display_data(industry_df)
                    display_df.to_excel(writer, sheet_name='行业板块', index=False)
                    
                    # 格式化工作表
                    worksheet = writer.sheets['行业板块']
                    ExcelExporter._format_worksheet(worksheet, len(display_df))
                    logger.info(f"行业板块数据已导出，共{len(industry_df)}条记录")

                # 添加数据统计摘要
                ExcelExporter._add_summary_sheet(writer, concept_df, industry_df)

            logger.info(f"数据已成功导出到: {filename}")
            return filename

        except Exception as e:
            logger.error(f"导出Excel文件失败: {e}")
            raise

    @staticmethod
    def _prepare_display_data(df: pd.DataFrame) -> pd.DataFrame:
        """准备用于显示的数据"""
        display_df = df.copy()
        
        # 定义列显示顺序
        column_order = [
            '板块名称', '涨跌幅(%)', '成交额(万元)', '热度排名变化', 
            '标签信息', '热度标签', 'ETF名称', 'ETF涨跌幅(%)', 
            '板块代码', '获取时间'
        ]
        
        # 只保留存在的列
        available_columns = [col for col in column_order if col in display_df.columns]
        display_df = display_df[available_columns]
        
        # 格式化数值列
        if '涨跌幅(%)' in display_df.columns:
            display_df['涨跌幅(%)'] = display_df['涨跌幅(%)'].round(2)
        if 'ETF涨跌幅(%)' in display_df.columns:
            display_df['ETF涨跌幅(%)'] = display_df['ETF涨跌幅(%)'].round(2)
        if '成交额(万元)' in display_df.columns:
            display_df['成交额(万元)'] = display_df['成交额(万元)'].round(2)
        
        return display_df

    @staticmethod
    def _format_worksheet(worksheet, data_rows):
        """格式化Excel工作表"""
        try:
            from openpyxl.styles import PatternFill, Font, Alignment, Border, Side

            # 设置表头样式
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            header_font = Font(color="FFFFFF", bold=True, size=12)
            header_alignment = Alignment(horizontal="center", vertical="center")

            # 设置边框
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # 格式化表头
            for cell in worksheet[1]:
                cell.fill = header_fill
                cell.font = header_font
                cell.alignment = header_alignment
                cell.border = thin_border

            # 格式化数据行
            for row in worksheet.iter_rows(min_row=2, max_row=data_rows + 1):
                for cell in row:
                    cell.border = thin_border
                    cell.alignment = Alignment(horizontal="center", vertical="center")

            # 自动调整列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter

                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                adjusted_width = min(max_length + 2, 50)  # 最大宽度50
                worksheet.column_dimensions[column_letter].width = adjusted_width

            # 设置行高
            for row in range(1, data_rows + 2):
                worksheet.row_dimensions[row].height = 20

            logger.info("Excel工作表格式化完成")

        except ImportError:
            logger.warning("openpyxl.styles 模块不可用，跳过格式化")
        except Exception as e:
            logger.warning(f"Excel格式化失败: {e}")

    @staticmethod
    def _add_summary_sheet(writer, concept_df: Optional[pd.DataFrame], industry_df: Optional[pd.DataFrame]):
        """添加数据统计摘要工作表"""
        try:
            summary_data = []

            # 基本统计信息
            summary_data.append(['数据统计摘要', ''])
            summary_data.append(['生成时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
            summary_data.append(['', ''])

            # 概念板块统计
            if concept_df is not None and not concept_df.empty:
                summary_data.append(['概念板块统计', ''])
                summary_data.append(['总数量', len(concept_df)])

                if '涨跌幅(%)' in concept_df.columns:
                    rise_count = len(concept_df[concept_df['涨跌幅(%)'] > 0])
                    fall_count = len(concept_df[concept_df['涨跌幅(%)'] < 0])
                    flat_count = len(concept_df[concept_df['涨跌幅(%)'] == 0])

                    summary_data.append(['上涨板块数', rise_count])
                    summary_data.append(['下跌板块数', fall_count])
                    summary_data.append(['平盘板块数', flat_count])
                    summary_data.append(['平均涨跌幅(%)', round(concept_df['涨跌幅(%)'].mean(), 2)])
                    summary_data.append(['最大涨幅(%)', round(concept_df['涨跌幅(%)'].max(), 2)])
                    summary_data.append(['最大跌幅(%)', round(concept_df['涨跌幅(%)'].min(), 2)])

                if '成交额(万元)' in concept_df.columns:
                    summary_data.append(['总成交额(万元)', round(concept_df['成交额(万元)'].sum(), 2)])
                    summary_data.append(['平均成交额(万元)', round(concept_df['成交额(万元)'].mean(), 2)])

                summary_data.append(['', ''])

            # 行业板块统计
            if industry_df is not None and not industry_df.empty:
                summary_data.append(['行业板块统计', ''])
                summary_data.append(['总数量', len(industry_df)])

                if '涨跌幅(%)' in industry_df.columns:
                    rise_count = len(industry_df[industry_df['涨跌幅(%)'] > 0])
                    fall_count = len(industry_df[industry_df['涨跌幅(%)'] < 0])
                    flat_count = len(industry_df[industry_df['涨跌幅(%)'] == 0])

                    summary_data.append(['上涨板块数', rise_count])
                    summary_data.append(['下跌板块数', fall_count])
                    summary_data.append(['平盘板块数', flat_count])
                    summary_data.append(['平均涨跌幅(%)', round(industry_df['涨跌幅(%)'].mean(), 2)])
                    summary_data.append(['最大涨幅(%)', round(industry_df['涨跌幅(%)'].max(), 2)])
                    summary_data.append(['最大跌幅(%)', round(industry_df['涨跌幅(%)'].min(), 2)])

                if '成交额(万元)' in industry_df.columns:
                    summary_data.append(['总成交额(万元)', round(industry_df['成交额(万元)'].sum(), 2)])
                    summary_data.append(['平均成交额(万元)', round(industry_df['成交额(万元)'].mean(), 2)])

            # 创建摘要DataFrame
            summary_df = pd.DataFrame(summary_data, columns=['项目', '数值'])
            summary_df.to_excel(writer, sheet_name='数据摘要', index=False)

            # 格式化摘要工作表
            if '数据摘要' in writer.sheets:
                worksheet = writer.sheets['数据摘要']
                ExcelExporter._format_worksheet(worksheet, len(summary_df))

            logger.info("数据摘要工作表已添加")

        except Exception as e:
            logger.warning(f"添加摘要工作表失败: {e}")


def print_data_summary(concept_df: Optional[pd.DataFrame], industry_df: Optional[pd.DataFrame]):
    """打印数据摘要到控制台"""
    print("\n" + "="*60)
    print("同花顺板块数据获取结果")
    print("="*60)

    if concept_df is not None and not concept_df.empty:
        print(f"\n📊 概念板块数据: {len(concept_df)} 条")
        if '涨跌幅(%)' in concept_df.columns:
            rise_count = len(concept_df[concept_df['涨跌幅(%)'] > 0])
            fall_count = len(concept_df[concept_df['涨跌幅(%)'] < 0])
            print(f"   ↗️ 上涨: {rise_count} 个  ↘️ 下跌: {fall_count} 个")
            print(f"   📈 平均涨跌幅: {concept_df['涨跌幅(%)'].mean():.2f}%")

            # 显示涨幅前5名
            top_gainers = concept_df.nlargest(5, '涨跌幅(%)')
            print(f"\n   🔥 概念板块涨幅前5名:")
            for _, row in top_gainers.iterrows():
                print(f"      {row['板块名称']}: {row['涨跌幅(%)']:.2f}%")

    if industry_df is not None and not industry_df.empty:
        print(f"\n🏭 行业板块数据: {len(industry_df)} 条")
        if '涨跌幅(%)' in industry_df.columns:
            rise_count = len(industry_df[industry_df['涨跌幅(%)'] > 0])
            fall_count = len(industry_df[industry_df['涨跌幅(%)'] < 0])
            print(f"   ↗️ 上涨: {rise_count} 个  ↘️ 下跌: {fall_count} 个")
            print(f"   📈 平均涨跌幅: {industry_df['涨跌幅(%)'].mean():.2f}%")

            # 显示涨幅前5名
            top_gainers = industry_df.nlargest(5, '涨跌幅(%)')
            print(f"\n   🔥 行业板块涨幅前5名:")
            for _, row in top_gainers.iterrows():
                print(f"      {row['板块名称']}: {row['涨跌幅(%)']:.2f}%")

    print("\n" + "="*60)


def main():
    """主函数"""
    try:
        print("🚀 开始获取同花顺板块数据...")

        # 创建数据获取器
        fetcher = THSPlateDataFetcher()

        # 获取所有数据
        concept_data, industry_data = fetcher.get_all_data()

        if concept_data is None and industry_data is None:
            logger.error("❌ 未能获取到任何板块数据")
            sys.exit(1)

        # 打印数据摘要
        print_data_summary(concept_data, industry_data)

        # 导出到Excel
        logger.info("📊 正在导出数据到Excel...")
        excel_file = ExcelExporter.export_to_excel(concept_data, industry_data)

        print(f"\n✅ 数据导出完成!")
        print(f"📁 文件位置: {excel_file}")

        # 显示文件大小
        try:
            file_size = os.path.getsize(excel_file) / 1024  # KB
            print(f"📏 文件大小: {file_size:.1f} KB")
        except:
            pass

        print("\n🎉 程序执行完成!")

    except KeyboardInterrupt:
        logger.info("⚠️ 程序被用户中断")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
