#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同花顺板块数据获取工具
获取概念板块和行业板块的热度数据，支持数据库存储和Excel导出
"""

import argparse
import json
import logging
import os
import sys
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple

import pandas as pd
import psycopg2
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 配置日志
def setup_logging():
    """设置日志配置"""
    try:
        log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'result')
        os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, 'plate_data.log')

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(log_file, encoding='utf-8')
            ]
        )
    except Exception as e:
        # 如果日志文件创建失败，只使用控制台输出
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler()]
        )
        print(f"警告：无法创建日志文件，仅使用控制台输出: {e}")

# 延迟初始化日志
logger = logging.getLogger(__name__)

# PostgreSQL 数据库连接配置
POSTGRESQL_CONFIG = {
    'host': os.environ.get('PG_HOST', '127.0.0.1'),
    'user': os.environ.get('PG_USER', 'root'),
    'password': os.environ.get('PG_PASSWORD', '123629He'),
    'database': os.environ.get('PG_DATABASE', 'tushare'),
    'port': os.environ.get('PG_PORT', '5432')
}

# API配置
API_CONFIG = {
    'concept_url': 'https://dq.10jqka.com.cn/fuyao/hot_list_data/out/hot_list/v1/plate?type=concept',
    'industry_url': 'https://dq.10jqka.com.cn/fuyao/hot_list_data/out/hot_list/v1/plate?type=industry',
    'timeout': 30,
    'max_retries': 3,
    'backoff_factor': 1
}

# 请求头配置
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Referer': 'https://dq.10jqka.com.cn/',
}


class PlateDataFetcher:
    """板块数据获取器"""
    
    def __init__(self):
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """创建带重试机制的请求会话"""
        session = requests.Session()

        # 禁用代理
        session.proxies = {
            'http': None,
            'https': None,
        }

        # 配置重试策略
        retry_strategy = Retry(
            total=API_CONFIG['max_retries'],
            backoff_factor=API_CONFIG['backoff_factor'],
            status_forcelist=[429, 500, 502, 503, 504],
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        session.headers.update(HEADERS)

        return session
    
    def fetch_plate_data(self, plate_type: str) -> Optional[Dict]:
        """
        获取板块数据
        
        Args:
            plate_type: 板块类型，'concept' 或 'industry'
            
        Returns:
            Dict: API响应数据，失败时返回None
        """
        if plate_type not in ['concept', 'industry']:
            raise ValueError("plate_type must be 'concept' or 'industry'")
        
        url = API_CONFIG['concept_url'] if plate_type == 'concept' else API_CONFIG['industry_url']
        
        try:
            logger.info(f"正在获取{plate_type}板块数据...")
            response = self.session.get(url, timeout=API_CONFIG['timeout'])
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('status_code') != 0:
                logger.error(f"API返回错误: {data.get('status_msg', 'Unknown error')}")
                return None
            
            logger.info(f"成功获取{plate_type}板块数据，共{len(data['data']['plate_list'])}条记录")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"获取{plate_type}板块数据失败: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"解析{plate_type}板块数据JSON失败: {e}")
            return None
        except Exception as e:
            logger.error(f"获取{plate_type}板块数据时发生未知错误: {e}")
            return None
    
    def get_concept_data(self) -> Optional[pd.DataFrame]:
        """获取概念板块数据"""
        data = self.fetch_plate_data('concept')
        if data:
            df = pd.DataFrame(data['data']['plate_list'])
            df['plate_type'] = 'concept'
            now = datetime.now()
            df['fetch_time'] = now
            df['fetch_date'] = now.date()
            # 数据类型转换
            df = self._convert_data_types(df)
            return df
        return None

    def get_industry_data(self) -> Optional[pd.DataFrame]:
        """获取行业板块数据"""
        data = self.fetch_plate_data('industry')
        if data:
            df = pd.DataFrame(data['data']['plate_list'])
            df['plate_type'] = 'industry'
            now = datetime.now()
            df['fetch_time'] = now
            df['fetch_date'] = now.date()
            # 数据类型转换
            df = self._convert_data_types(df)
            return df
        return None

    def _convert_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """转换数据类型并进行数据清洗"""
        if df.empty:
            return df

        # 数值型列
        numeric_columns = ['rise_and_fall', 'etf_rise_and_fall', 'rate', 'hot_rank_chg',
                          'market_id', 'etf_market_id', 'order']

        for col in numeric_columns:
            if col in df.columns:
                # 转换为数值类型，无法转换的设为NaN
                df[col] = pd.to_numeric(df[col], errors='coerce')

                # 处理整数字段的特殊情况
                if col in ['hot_rank_chg', 'market_id', 'etf_market_id', 'order']:
                    # 检查并处理异常值
                    if not df[col].empty:
                        # 将NaN转为None
                        df[col] = df[col].where(pd.notna(df[col]), None)

                        # 将浮点数转为整数（如果不是None）
                        def safe_int_convert(x):
                            if x is None or pd.isna(x):
                                return None
                            try:
                                # 转换为整数
                                int_val = int(float(x))
                                # 检查范围
                                max_safe_int = 9223372036854775807  # BIGINT最大值
                                min_safe_int = -9223372036854775808  # BIGINT最小值
                                if min_safe_int <= int_val <= max_safe_int:
                                    return int_val
                                else:
                                    logger.warning(f"字段 {col} 的值 {int_val} 超出BIGINT范围，设为None")
                                    return None
                            except (ValueError, OverflowError) as e:
                                logger.warning(f"字段 {col} 的值 {x} 转换失败: {e}，设为None")
                                return None

                        df[col] = df[col].apply(safe_int_convert)

        # 字符串列清理
        string_columns = ['name', 'code', 'hot_tag', 'etf_product_id', 'etf_name', 'tag']
        for col in string_columns:
            if col in df.columns:
                # 去除前后空格，将空字符串转为None
                df[col] = df[col].astype(str).str.strip()
                df[col] = df[col].replace('', None)
                df[col] = df[col].replace('nan', None)

        # 数据验证
        logger.debug(f"数据转换完成，共{len(df)}条记录")
        if logger.isEnabledFor(logging.DEBUG):
            # 显示数据质量统计
            for col in df.columns:
                null_count = df[col].isnull().sum()
                if null_count > 0:
                    logger.debug(f"列 {col} 有 {null_count} 个空值")

        return df
    
    def get_all_data(self) -> Tuple[Optional[pd.DataFrame], Optional[pd.DataFrame]]:
        """获取所有板块数据"""
        concept_data = self.get_concept_data()
        industry_data = self.get_industry_data()
        return concept_data, industry_data


class DatabaseManager:
    """数据库管理器"""
    
    @staticmethod
    def get_connection():
        """获取数据库连接"""
        try:
            conn = psycopg2.connect(**POSTGRESQL_CONFIG)
            return conn
        except psycopg2.Error as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    @staticmethod
    def init_tables():
        """初始化数据库表"""
        try:
            with DatabaseManager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 创建板块数据表
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS plate_hot_data (
                            id SERIAL PRIMARY KEY,
                            code VARCHAR(20) NOT NULL,
                            name VARCHAR(100) NOT NULL,
                            plate_type VARCHAR(20) NOT NULL,
                            rise_and_fall NUMERIC(10,4),
                            etf_rise_and_fall NUMERIC(10,4),
                            hot_rank_chg BIGINT,
                            market_id BIGINT,
                            hot_tag VARCHAR(100),
                            etf_product_id VARCHAR(20),
                            rate NUMERIC(20,2),
                            etf_name VARCHAR(100),
                            tag VARCHAR(100),
                            etf_market_id BIGINT,
                            order_rank BIGINT,
                            fetch_date DATE DEFAULT CURRENT_DATE,
                            fetch_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            UNIQUE(code, plate_type, fetch_date)
                        )
                    """)

                    # 添加列注释
                    cursor.execute("COMMENT ON COLUMN plate_hot_data.code IS '板块代码'")
                    cursor.execute("COMMENT ON COLUMN plate_hot_data.name IS '板块名称'")
                    cursor.execute("COMMENT ON COLUMN plate_hot_data.plate_type IS '板块类型(concept/industry)'")
                    cursor.execute("COMMENT ON COLUMN plate_hot_data.rise_and_fall IS '涨跌幅'")
                    cursor.execute("COMMENT ON COLUMN plate_hot_data.etf_rise_and_fall IS 'ETF涨跌幅'")
                    cursor.execute("COMMENT ON COLUMN plate_hot_data.hot_rank_chg IS '热度排名变化'")
                    cursor.execute("COMMENT ON COLUMN plate_hot_data.market_id IS '市场ID'")
                    cursor.execute("COMMENT ON COLUMN plate_hot_data.hot_tag IS '热度标签'")
                    cursor.execute("COMMENT ON COLUMN plate_hot_data.etf_product_id IS 'ETF产品ID'")
                    cursor.execute("COMMENT ON COLUMN plate_hot_data.rate IS '成交额'")
                    cursor.execute("COMMENT ON COLUMN plate_hot_data.etf_name IS 'ETF名称'")
                    cursor.execute("COMMENT ON COLUMN plate_hot_data.tag IS '标签'")
                    cursor.execute("COMMENT ON COLUMN plate_hot_data.etf_market_id IS 'ETF市场ID'")
                    cursor.execute("COMMENT ON COLUMN plate_hot_data.order_rank IS '排序'")
                    cursor.execute("COMMENT ON COLUMN plate_hot_data.fetch_date IS '获取日期'")
                    cursor.execute("COMMENT ON COLUMN plate_hot_data.fetch_time IS '获取时间'")
                    
                    # 创建索引
                    cursor.execute("""
                        CREATE INDEX IF NOT EXISTS idx_plate_hot_data_code 
                        ON plate_hot_data (code)
                    """)
                    
                    cursor.execute("""
                        CREATE INDEX IF NOT EXISTS idx_plate_hot_data_type 
                        ON plate_hot_data (plate_type)
                    """)
                    
                    cursor.execute("""
                        CREATE INDEX IF NOT EXISTS idx_plate_hot_data_time 
                        ON plate_hot_data (fetch_time)
                    """)
                    
                    conn.commit()
                    logger.info("数据库表初始化完成")
                    
        except psycopg2.Error as e:
            logger.error(f"初始化数据库表失败: {e}")
            raise
    
    @staticmethod
    def save_data(df: pd.DataFrame) -> int:
        """
        保存数据到数据库
        
        Args:
            df: 要保存的DataFrame
            
        Returns:
            int: 成功插入的记录数
        """
        if df is None or df.empty:
            logger.warning("没有数据需要保存")
            return 0
        
        try:
            with DatabaseManager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 准备插入数据
                    insert_sql = """
                        INSERT INTO plate_hot_data (
                            code, name, plate_type, rise_and_fall, etf_rise_and_fall,
                            hot_rank_chg, market_id, hot_tag, etf_product_id, rate,
                            etf_name, tag, etf_market_id, order_rank, fetch_date, fetch_time
                        ) VALUES (
                            %(code)s, %(name)s, %(plate_type)s, %(rise_and_fall)s, %(etf_rise_and_fall)s,
                            %(hot_rank_chg)s, %(market_id)s, %(hot_tag)s, %(etf_product_id)s, %(rate)s,
                            %(etf_name)s, %(tag)s, %(etf_market_id)s, %(order)s, %(fetch_date)s, %(fetch_time)s
                        ) ON CONFLICT (code, plate_type, fetch_date) DO UPDATE SET
                            name = EXCLUDED.name,
                            rise_and_fall = EXCLUDED.rise_and_fall,
                            etf_rise_and_fall = EXCLUDED.etf_rise_and_fall,
                            hot_rank_chg = EXCLUDED.hot_rank_chg,
                            market_id = EXCLUDED.market_id,
                            hot_tag = EXCLUDED.hot_tag,
                            etf_product_id = EXCLUDED.etf_product_id,
                            rate = EXCLUDED.rate,
                            etf_name = EXCLUDED.etf_name,
                            tag = EXCLUDED.tag,
                            etf_market_id = EXCLUDED.etf_market_id,
                            order_rank = EXCLUDED.order_rank,
                            fetch_time = EXCLUDED.fetch_time
                    """
                    
                    # 转换DataFrame为字典列表
                    records = df.to_dict('records')

                    # 调试：检查数据
                    logger.debug(f"准备插入 {len(records)} 条记录")
                    if records:
                        sample_record = records[0]
                        logger.debug(f"样本记录: {sample_record}")

                        # 检查可能有问题的字段
                        for key, value in sample_record.items():
                            if key in ['hot_rank_chg', 'market_id', 'etf_market_id', 'order']:
                                if value is not None:
                                    logger.debug(f"{key}: {value} (类型: {type(value)})")

                    # 进一步清理数据，确保类型正确
                    cleaned_records = []
                    for record in records:
                        cleaned_record = {}
                        for key, value in record.items():
                            if key in ['hot_rank_chg', 'market_id', 'etf_market_id', 'order']:
                                # 确保整数字段是正确的类型
                                if value is not None and not pd.isna(value):
                                    try:
                                        cleaned_record[key] = int(float(value))
                                    except (ValueError, OverflowError):
                                        cleaned_record[key] = None
                                else:
                                    cleaned_record[key] = None
                            elif key in ['fetch_time']:
                                # 处理时间戳
                                if hasattr(value, 'to_pydatetime'):
                                    cleaned_record[key] = value.to_pydatetime()
                                else:
                                    cleaned_record[key] = value
                            elif key in ['fetch_date']:
                                # 处理日期
                                if hasattr(value, 'date'):
                                    cleaned_record[key] = value.date() if hasattr(value, 'date') else value
                                else:
                                    cleaned_record[key] = value
                            else:
                                cleaned_record[key] = value
                        cleaned_records.append(cleaned_record)

                    # 批量插入
                    cursor.executemany(insert_sql, cleaned_records)
                    inserted_count = cursor.rowcount
                    
                    conn.commit()
                    logger.info(f"成功保存{inserted_count}条板块数据到数据库")
                    return inserted_count
                    
        except psycopg2.Error as e:
            logger.error(f"保存数据到数据库失败: {e}")
            raise
        except Exception as e:
            logger.error(f"保存数据时发生未知错误: {e}")
            raise


class ExcelExporter:
    """Excel导出器"""
    
    @staticmethod
    def export_to_excel(concept_df: Optional[pd.DataFrame], 
                       industry_df: Optional[pd.DataFrame], 
                       filename: Optional[str] = None) -> str:
        """
        导出数据到Excel文件
        
        Args:
            concept_df: 概念板块数据
            industry_df: 行业板块数据
            filename: 文件名，如果为None则自动生成
            
        Returns:
            str: 导出的文件路径
        """
        # 确保输出到result目录
        result_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'result')
        os.makedirs(result_dir, exist_ok=True)

        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(result_dir, f"板块热度数据_{timestamp}.xlsx")
        elif not os.path.isabs(filename):
            filename = os.path.join(result_dir, filename)
        
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                if concept_df is not None and not concept_df.empty:
                    # 优化列显示
                    display_df = concept_df.copy()
                    # 重新排列列顺序，突出重要信息
                    column_order = ['name', 'rise_and_fall', 'rate', 'hot_rank_chg', 'tag', 'hot_tag',
                                  'etf_name', 'etf_rise_and_fall', 'code', 'fetch_time']
                    # 只保留存在的列
                    available_columns = [col for col in column_order if col in display_df.columns]
                    display_df = display_df[available_columns]

                    display_df.to_excel(writer, sheet_name='概念板块', index=False)

                    # 格式化工作表
                    worksheet = writer.sheets['概念板块']
                    ExcelExporter._format_worksheet(worksheet, len(display_df))

                    logger.info(f"概念板块数据已导出，共{len(concept_df)}条记录")

                if industry_df is not None and not industry_df.empty:
                    # 优化列显示
                    display_df = industry_df.copy()
                    # 重新排列列顺序
                    column_order = ['name', 'rise_and_fall', 'rate', 'hot_rank_chg', 'tag', 'hot_tag',
                                  'etf_name', 'etf_rise_and_fall', 'code', 'fetch_time']
                    # 只保留存在的列
                    available_columns = [col for col in column_order if col in display_df.columns]
                    display_df = display_df[available_columns]

                    display_df.to_excel(writer, sheet_name='行业板块', index=False)

                    # 格式化工作表
                    worksheet = writer.sheets['行业板块']
                    self._format_worksheet(worksheet, len(display_df))

                    logger.info(f"行业板块数据已导出，共{len(industry_df)}条记录")

            logger.info(f"数据已成功导出到: {filename}")
            return filename

        except Exception as e:
            logger.error(f"导出Excel文件失败: {e}")
            raise

    @staticmethod
    def _format_worksheet(worksheet, data_rows):
        """格式化Excel工作表"""
        try:
            from openpyxl.styles import PatternFill, Font, Alignment, Border, Side

            # 设置表头样式
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            header_font = Font(color="FFFFFF", bold=True)
            header_alignment = Alignment(horizontal="center", vertical="center")

            # 设置边框
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # 格式化表头
            for cell in worksheet[1]:
                cell.fill = header_fill
                cell.font = header_font
                cell.alignment = header_alignment
                cell.border = thin_border

            # 格式化数据行
            for row in worksheet.iter_rows(min_row=2, max_row=data_rows + 1):
                for cell in row:
                    cell.border = thin_border
                    cell.alignment = Alignment(horizontal="center", vertical="center")

            # 自动调整列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter

                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                adjusted_width = min(max_length + 2, 50)  # 最大宽度50
                worksheet.column_dimensions[column_letter].width = adjusted_width

            logger.info("Excel工作表格式化完成")

        except ImportError:
            logger.warning("openpyxl.styles 模块不可用，跳过格式化")
        except Exception as e:
            logger.warning(f"Excel格式化失败: {e}")
            # 格式化失败不影响数据导出


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='获取同花顺板块热度数据')
    parser.add_argument('--save-db', action='store_true', help='保存数据到数据库')
    parser.add_argument('--export-excel', action='store_true', help='导出数据到Excel')
    parser.add_argument('--output', '-o', type=str, help='输出文件名（不包含路径，自动保存到result目录）')
    parser.add_argument('--init-db', action='store_true', help='初始化数据库表')
    parser.add_argument('--concept-only', action='store_true', help='仅获取概念板块数据')
    parser.add_argument('--industry-only', action='store_true', help='仅获取行业板块数据')
    parser.add_argument('--verbose', '-v', action='store_true', help='显示详细信息')

    args = parser.parse_args()

    # 初始化日志
    setup_logging()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # 初始化数据库表（如果需要）
        if args.init_db:
            DatabaseManager.init_tables()
            return
        
        # 获取数据
        fetcher = PlateDataFetcher()
        concept_data = None
        industry_data = None

        # 根据参数决定获取哪些数据
        if args.concept_only:
            logger.info("仅获取概念板块数据")
            concept_data = fetcher.get_concept_data()
        elif args.industry_only:
            logger.info("仅获取行业板块数据")
            industry_data = fetcher.get_industry_data()
        else:
            logger.info("获取所有板块数据")
            concept_data, industry_data = fetcher.get_all_data()

        if concept_data is None and industry_data is None:
            logger.error("未能获取到任何板块数据")
            sys.exit(1)

        # 显示获取到的数据统计
        total_records = 0
        if concept_data is not None:
            total_records += len(concept_data)
            logger.info(f"获取到概念板块数据: {len(concept_data)} 条")
        if industry_data is not None:
            total_records += len(industry_data)
            logger.info(f"获取到行业板块数据: {len(industry_data)} 条")
        logger.info(f"总计获取数据: {total_records} 条")
        
        # 保存到数据库
        if args.save_db:
            DatabaseManager.init_tables()  # 确保表存在
            total_saved = 0
            if concept_data is not None:
                total_saved += DatabaseManager.save_data(concept_data)
            if industry_data is not None:
                total_saved += DatabaseManager.save_data(industry_data)
            logger.info(f"总共保存了{total_saved}条记录到数据库")
        
        # 导出到Excel
        if args.export_excel:
            ExcelExporter.export_to_excel(concept_data, industry_data, args.output)
        
        # 如果没有指定任何输出选项，默认显示数据摘要和保存到数据库
        if not args.save_db and not args.export_excel:
            logger.info("未指定输出选项，默认保存到数据库并显示数据摘要")
            # 默认保存到数据库
            DatabaseManager.init_tables()
            total_saved = 0
            if concept_data is not None:
                total_saved += DatabaseManager.save_data(concept_data)
            if industry_data is not None:
                total_saved += DatabaseManager.save_data(industry_data)
            logger.info(f"默认保存了{total_saved}条记录到数据库")

            # 显示数据摘要
            if concept_data is not None:
                print("\n=== 概念板块数据摘要 ===")
                print(f"总数: {len(concept_data)}")
                # 选择关键列显示
                display_columns = []
                for col in ['name', 'rise_and_fall', 'rate', 'hot_rank_chg', 'tag']:
                    if col in concept_data.columns:
                        display_columns.append(col)
                if display_columns:
                    print(concept_data[display_columns].head(10).to_string(index=False))

            if industry_data is not None:
                print("\n=== 行业板块数据摘要 ===")
                print(f"总数: {len(industry_data)}")
                # 选择关键列显示
                display_columns = []
                for col in ['name', 'rise_and_fall', 'rate', 'hot_rank_chg', 'tag']:
                    if col in industry_data.columns:
                        display_columns.append(col)
                if display_columns:
                    print(industry_data[display_columns].head(10).to_string(index=False))
        
        logger.info("程序执行完成")
        
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
