# 同花顺板块数据Excel导出工具

这是一个专门用于获取同花顺概念板块和行业板块数据并导出到Excel文件的Python工具。

## 功能特性

- 🚀 **实时数据获取**: 从同花顺API获取最新的板块热度数据
- 📊 **双板块支持**: 同时获取概念板块和行业板块数据
- 📈 **Excel导出**: 自动导出到格式化的Excel文件
- 📋 **数据摘要**: 包含数据统计摘要工作表
- 🎨 **美观格式**: 自动格式化Excel表格（表头样式、边框、列宽等）
- 📝 **控制台摘要**: 在控制台显示数据获取结果摘要
- 🛡️ **错误处理**: 完善的异常处理机制

## 数据字段说明

### 概念板块/行业板块数据字段
- `板块名称`: 板块名称
- `板块代码`: 板块代码
- `涨跌幅(%)`: 板块涨跌幅百分比
- `ETF涨跌幅(%)`: 对应ETF涨跌幅百分比
- `成交额(万元)`: 成交额（万元）
- `热度排名变化`: 热度排名变化
- `标签信息`: 标签信息（如"8家涨停"）
- `热度标签`: 热度标签（如"连续46天上榜"）
- `ETF名称`: 对应ETF名称
- `获取时间`: 数据获取时间

### 数据摘要工作表包含
- 生成时间
- 概念板块统计（总数量、上涨/下跌数量、平均涨跌幅等）
- 行业板块统计（总数量、上涨/下跌数量、平均涨跌幅等）

## 安装依赖

```bash
pip install pandas requests openpyxl urllib3
```

## 使用方法

### 1. 直接运行

```bash
# 激活tushare环境（如果使用conda）
conda activate tushare

# 运行脚本
python tonghuashun/ths_plate_data_excel.py
```

### 2. Python代码调用

```python
from tonghuashun.ths_plate_data_excel import THSPlateDataFetcher, ExcelExporter

# 创建数据获取器
fetcher = THSPlateDataFetcher()

# 获取所有数据
concept_data, industry_data = fetcher.get_all_data()

# 导出到Excel
excel_file = ExcelExporter.export_to_excel(concept_data, industry_data)
print(f"数据已导出到: {excel_file}")
```

### 3. 自定义文件名

```python
# 指定自定义文件名
excel_file = ExcelExporter.export_to_excel(
    concept_data, 
    industry_data, 
    "我的板块数据_20240101.xlsx"
)
```

## 输出文件

### 文件位置
- 默认保存在 `tonghuashun/result/` 目录下
- 文件名格式: `同花顺板块数据_YYYYMMDD_HHMMSS.xlsx`

### Excel工作表
1. **概念板块**: 包含所有概念板块数据
2. **行业板块**: 包含所有行业板块数据  
3. **数据摘要**: 包含统计信息和数据分析

### Excel格式特性
- 蓝色表头背景，白色粗体字
- 自动调整列宽
- 所有单元格居中对齐
- 完整边框
- 数值字段保留2位小数

## 控制台输出示例

```
🚀 开始获取同花顺板块数据...
2024-01-01 10:30:00 - INFO - 正在获取concept板块数据...
2024-01-01 10:30:01 - INFO - 成功获取concept板块数据，共156条记录
2024-01-01 10:30:02 - INFO - 正在获取industry板块数据...
2024-01-01 10:30:03 - INFO - 成功获取industry板块数据，共65条记录

============================================================
同花顺板块数据获取结果
============================================================

📊 概念板块数据: 156 条
   ↗️ 上涨: 89 个  ↘️ 下跌: 67 个
   📈 平均涨跌幅: 1.23%

   🔥 概念板块涨幅前5名:
      人工智能: 5.67%
      新能源汽车: 4.32%
      芯片概念: 3.89%
      医疗器械: 3.45%
      5G概念: 2.98%

🏭 行业板块数据: 65 条
   ↗️ 上涨: 38 个  ↘️ 下跌: 27 个
   📈 平均涨跌幅: 0.89%

   🔥 行业板块涨幅前5名:
      电子信息: 4.12%
      医药生物: 3.67%
      新能源: 3.23%
      汽车制造: 2.89%
      计算机: 2.45%

============================================================

2024-01-01 10:30:04 - INFO - 📊 正在导出数据到Excel...
2024-01-01 10:30:05 - INFO - 概念板块数据已导出，共156条记录
2024-01-01 10:30:05 - INFO - 行业板块数据已导出，共65条记录
2024-01-01 10:30:05 - INFO - 数据摘要工作表已添加
2024-01-01 10:30:05 - INFO - 数据已成功导出到: F:/workspace/pythonProjects/hzh-tushare/tonghuashun/result/同花顺板块数据_20240101_103005.xlsx

✅ 数据导出完成!
📁 文件位置: F:/workspace/pythonProjects/hzh-tushare/tonghuashun/result/同花顺板块数据_20240101_103005.xlsx
📏 文件大小: 45.2 KB

🎉 程序执行完成!
```

## 注意事项

1. **网络连接**: 需要稳定的网络连接访问同花顺API
2. **API限制**: 请合理使用API，避免过于频繁的请求
3. **数据时效性**: 板块数据实时变化，建议定期获取最新数据
4. **文件权限**: 确保对输出目录有写入权限

## 错误处理

程序包含完善的错误处理机制：
- 网络请求失败自动重试
- JSON解析错误处理
- 文件操作异常处理
- 用户中断处理（Ctrl+C）

## 技术特性

- 使用requests库进行HTTP请求，支持重试机制
- 使用pandas进行数据处理和清洗
- 使用openpyxl进行Excel文件操作和格式化
- 支持中文列名和数据显示
- 自动数据类型转换和异常值处理

## 扩展功能建议

可以基于此工具进行扩展：
- 添加定时任务功能
- 集成到数据库存储
- 添加数据可视化图表
- 实现邮件自动发送
- 集成到量化交易系统
